import React, { useState } from 'react';
import { Calendar, Plus, TrendingUp, TrendingDown } from 'lucide-react';

const Journal = () => {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [showAddForm, setShowAddForm] = useState(false);

  return (
    <div className="min-h-screen px-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Trading Journal</h1>
          <p className="text-gray-400">Log and track your trading activities with detailed performance analytics</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-6 mb-8">
          <div className="lg:col-span-2">
            <div className="glass-card p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold">Trades for {selectedDate}</h2>
                <button 
                  onClick={() => setShowAddForm(!showAddForm)}
                  className="glass-button flex items-center space-x-2 px-4 py-2"
                >
                  <Plus size={16} />
                  <span>Add Trade</span>
                </button>
              </div>

              <div className="mb-6">
                <label className="block text-sm font-medium mb-2">Select Date</label>
                <input 
                  type="date" 
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="p-3 bg-white/5 border border-white/10 rounded-lg focus:ring-2 focus:ring-cyan-400 focus:border-transparent"
                />
              </div>

              {showAddForm && (
                <div className="glass-card p-6 mb-6 bg-white/5">
                  <h3 className="text-lg font-semibold mb-4">Add New Trade</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Symbol</label>
                      <input 
                        type="text" 
                        placeholder="e.g., RELIANCE" 
                        className="w-full p-3 bg-white/5 border border-white/10 rounded-lg"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Quantity</label>
                      <input 
                        type="number" 
                        placeholder="100" 
                        className="w-full p-3 bg-white/5 border border-white/10 rounded-lg"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Buy Price</label>
                      <input 
                        type="number" 
                        step="0.01" 
                        placeholder="2500.00" 
                        className="w-full p-3 bg-white/5 border border-white/10 rounded-lg"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Sell Price</label>
                      <input 
                        type="number" 
                        step="0.01" 
                        placeholder="2550.00" 
                        className="w-full p-3 bg-white/5 border border-white/10 rounded-lg"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Action</label>
                      <select className="w-full p-3 bg-white/5 border border-white/10 rounded-lg">
                        <option>Buy</option>
                        <option>Sell</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Fees</label>
                      <input 
                        type="number" 
                        step="0.01" 
                        placeholder="15.00" 
                        className="w-full p-3 bg-white/5 border border-white/10 rounded-lg"
                      />
                    </div>
                  </div>
                  <div className="mt-4">
                    <label className="block text-sm font-medium mb-2">Notes</label>
                    <textarea 
                      placeholder="Add your observations and strategy notes..."
                      className="w-full p-3 bg-white/5 border border-white/10 rounded-lg h-24 resize-none"
                    />
                  </div>
                  <div className="flex space-x-4 mt-6">
                    <button className="glass-button px-6 py-2">Save Trade</button>
                    <button 
                      onClick={() => setShowAddForm(false)}
                      className="px-6 py-2 text-gray-400 hover:text-white"
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              )}

              <div className="space-y-4">
                <div className="text-center py-8 text-gray-400">
                  <Calendar size={48} className="mx-auto mb-4" />
                  <p>No trades found for this date</p>
                  <p className="text-sm">Add your first trade to get started</p>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold mb-4">Performance Summary</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Total Trades</span>
                  <span className="text-white font-semibold">127</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Win Rate</span>
                  <span className="text-green-400 font-semibold flex items-center">
                    <TrendingUp size={16} className="mr-1" />
                    68%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Total PnL</span>
                  <span className="text-green-400 font-semibold">+₹12,450</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Best Trade</span>
                  <span className="text-green-400 font-semibold">+₹2,340</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-400">Worst Trade</span>
                  <span className="text-red-400 font-semibold">-₹890</span>
                </div>
              </div>
            </div>

            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold mb-4">Quick Tags</h3>
              <div className="space-y-2">
                <div className="flex flex-wrap gap-2">
                  <span className="px-3 py-1 bg-green-400/20 text-green-400 rounded-full text-sm">Learning</span>
                  <span className="px-3 py-1 bg-red-400/20 text-red-400 rounded-full text-sm">Bad Trade</span>
                  <span className="px-3 py-1 bg-blue-400/20 text-blue-400 rounded-full text-sm">Strategy A</span>
                  <span className="px-3 py-1 bg-purple-400/20 text-purple-400 rounded-full text-sm">Scalping</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Journal;
