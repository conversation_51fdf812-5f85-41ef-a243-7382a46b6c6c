import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  BarChart3, 
  BookOpen, 
  Brain, 
  TrendingUp, 
  Globe, 
  Settings,
  LogIn,
  Github,
  Phone
} from 'lucide-react';
import FeatureCard from './FeatureCard';

const Dashboard = () => {
  const navigate = useNavigate();
  const [isLoggedIn, setIsLoggedIn] = React.useState(false);

  const features = [
    {
      title: 'Console',
      description: 'Advanced market analysis with statistical insights, technical indicators, and AI-driven suggestions.',
      icon: BarChart3,
      metrics: isLoggedIn ? [
        { label: 'Active Analysis', value: '3' },
        { label: 'Last Update', value: '2m ago' }
      ] : []
    },
    {
      title: 'Journal',
      description: 'Log and track your trading activities with detailed performance analytics and insights.',
      icon: BookOpen,
      metrics: isLoggedIn ? [
        { label: 'Total Trades', value: '127' },
        { label: 'Win Rate', value: '68%', trend: 'up' as const }
      ] : []
    },
    {
      title: 'Mindsage',
      description: 'AI investment guide analyzing fundamentals, technicals, and market factors for comprehensive insights.',
      icon: Brain,
      status: 'coming-soon' as const
    },
    {
      title: 'Algo Trading',
      description: 'Automated paper trading with custom algorithms and natural language command execution.',
      icon: TrendingUp,
      metrics: isLoggedIn ? [
        { label: 'Active Algos', value: '2' },
        { label: 'Paper PnL', value: '+5.2%', trend: 'up' as const }
      ] : []
    },
    {
      title: 'Global Sentiment',
      description: 'Real-time market sentiment analysis from global financial news and social media sources.',
      icon: Globe,
      metrics: isLoggedIn ? [
        { label: 'Market Sentiment', value: 'Positive' },
        { label: 'News Sources', value: '12' }
      ] : []
    },
    {
      title: 'Model Trainer',
      description: 'Train personalized AI models with your study materials for customized market analysis.',
      icon: Settings,
      metrics: isLoggedIn ? [
        { label: 'Trained Models', value: '3' },
        { label: 'Accuracy', value: '87%', trend: 'up' as const }
      ] : []
    }
  ];

  const handleFeatureClick = (title: string) => {
    const routes: { [key: string]: string } = {
      'Console': '/console',
      'Journal': '/journal',
      'Algo Trading': '/algo-trading',
      'Global Sentiment': '/sentiment',
      'Model Trainer': '/model-trainer'
    };
    
    if (routes[title]) {
      navigate(routes[title]);
    }
  };

  const handleLogin = (method: string) => {
    console.log(`Login with ${method}`);
    setIsLoggedIn(true);
  };

  if (!isLoggedIn) {
    return (
      <div className="min-h-screen px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16 pt-8">
            <h1 className="text-5xl font-bold mb-6 bg-gradient-to-r from-cyan-400 via-blue-500 to-purple-500 bg-clip-text text-transparent">
              Welcome to Slynqix
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
              A comprehensive stock market analysis & paper trading platform powered by AI and advanced analytics
            </p>
            
            <div className="glass-card p-8 max-w-md mx-auto mb-16">
              <h2 className="text-2xl font-semibold mb-6">Get Started</h2>
              <div className="space-y-4">
                <button 
                  onClick={() => handleLogin('Google')}
                  className="w-full glass-button flex items-center justify-center space-x-3 py-3"
                >
                  <LogIn size={20} />
                  <span>Continue with Google</span>
                </button>
                <button 
                  onClick={() => handleLogin('GitHub')}
                  className="w-full glass-button flex items-center justify-center space-x-3 py-3"
                >
                  <Github size={20} />
                  <span>Continue with GitHub</span>
                </button>
                <button 
                  onClick={() => handleLogin('Phone')}
                  className="w-full glass-button flex items-center justify-center space-x-3 py-3"
                >
                  <Phone size={20} />
                  <span>Continue with Phone</span>
                </button>
              </div>
            </div>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {features.map((feature, index) => (
              <FeatureCard
                key={index}
                title={feature.title}
                description={feature.description}
                icon={feature.icon}
                status={feature.status}
              />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen px-4">
      <div className="max-w-7xl mx-auto">
        <div className="glass-card p-8 mb-8 liquid-gradient">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">Welcome back!</h1>
              <p className="text-gray-300">Here's your market overview for today</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-green-400">+12.3%</div>
              <div className="text-sm text-gray-400">Portfolio Performance</div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <div className="glass-card p-4 text-center">
            <div className="text-2xl font-bold text-cyan-400">₹2,45,680</div>
            <div className="text-xs text-gray-400">Paper Portfolio</div>
          </div>
          <div className="glass-card p-4 text-center">
            <div className="text-2xl font-bold text-green-400">+5.2%</div>
            <div className="text-xs text-gray-400">Today's PnL</div>
          </div>
          <div className="glass-card p-4 text-center">
            <div className="text-2xl font-bold text-purple-400">2</div>
            <div className="text-xs text-gray-400">Active Algos</div>
          </div>
          <div className="glass-card p-4 text-center">
            <div className="text-2xl font-bold text-blue-400">24,580</div>
            <div className="text-xs text-gray-400">Nifty 50</div>
          </div>
        </div>

        <div className="glass-card p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Live Market Overview</h2>
          <div className="h-64 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <BarChart3 size={48} className="text-cyan-400 mx-auto mb-4" />
              <p className="text-gray-400">TradingView Chart Integration</p>
              <p className="text-sm text-gray-500">Real-time Nifty 50 data</p>
            </div>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <FeatureCard
              key={index}
              title={feature.title}
              description={feature.description}
              icon={feature.icon}
              status={feature.status}
              metrics={feature.metrics}
              onClick={() => handleFeatureClick(feature.title)}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
