
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import AppSidebar from "./components/AppSidebar";
import Dashboard from "./components/Dashboard";
import Console from "./pages/Console";
import Journal from "./pages/Journal";
import Profile from "./pages/Profile";
import AlgoTrading from "./pages/AlgoTrading";
import GlobalSentiment from "./pages/GlobalSentiment";
import ModelTrainer from "./pages/ModelTrainer";
import Mindsage from "./pages/Mindsage";
import NotFound from "./pages/NotFound";
import { Brain } from 'lucide-react';

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <SidebarProvider>
          <div className="min-h-screen w-full flex">
            <AppSidebar />
            <main className="flex-1">
              <div className="p-4">
                <SidebarTrigger />
              </div>
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/console" element={<Console />} />
                <Route path="/journal" element={<Journal />} />
                <Route path="/profile" element={<Profile />} />
                <Route path="/algo-trading" element={<AlgoTrading />} />
                <Route path="/sentiment" element={<GlobalSentiment />} />
                <Route path="/model-trainer" element={<ModelTrainer />} />
                <Route path="/mindsage" element={<Mindsage />} />
                <Route path="*" element={<NotFound />} />
              </Routes>
            </main>
          </div>
        </SidebarProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
