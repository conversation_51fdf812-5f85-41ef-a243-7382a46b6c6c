
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  BarChart3, 
  BookOpen, 
  Brain, 
  TrendingUp, 
  Globe, 
  Settings,
  User,
  Home
} from 'lucide-react';

const Navigation = () => {
  const location = useLocation();
  
  const navItems = [
    { path: '/', label: 'Dashboard', icon: Home },
    { path: '/console', label: 'Console', icon: BarChart3 },
    { path: '/journal', label: 'Journal', icon: BookOpen },
    { path: '/mindsage', label: 'Mindsage', icon: Brain },
    { path: '/algo-trading', label: 'Algo Trading', icon: TrendingUp },
    { path: '/sentiment', label: 'Global Sentiment', icon: Globe },
    { path: '/model-trainer', label: 'Model Trainer', icon: Settings },
    { path: '/profile', label: 'Profile', icon: User },
  ];

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 glass-card mx-4 mt-4">
      <div className="flex items-center justify-between px-6 py-4">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-lg flex items-center justify-center pulse-glow">
            <span className="text-white font-bold text-sm">S</span>
          </div>
          <span className="text-xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
            Slynqix
          </span>
        </div>
        
        <div className="hidden md:flex items-center space-x-1">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;
            
            return (
              <Link
                key={item.path}
                to={item.path}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-300 ${
                  isActive 
                    ? 'bg-gradient-to-r from-cyan-500/20 to-blue-500/20 text-cyan-400' 
                    : 'text-gray-300 hover:text-white hover:bg-white/10'
                }`}
              >
                <Icon size={18} />
                <span className="text-sm font-medium">{item.label}</span>
              </Link>
            );
          })}
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-400">
            {new Date().toLocaleTimeString()}
          </div>
          <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full"></div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
