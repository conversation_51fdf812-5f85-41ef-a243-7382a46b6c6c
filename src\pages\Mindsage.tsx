
import React, { useState } from 'react';
import { Brain, Search, TrendingUp, BarChart3, DollarSign, Calendar, AlertCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';

const Mindsage = () => {
  const [symbol, setSymbol] = useState('NIFTY50');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisData, setAnalysisData] = useState(null);

  const handleAnalyze = () => {
    setIsAnalyzing(true);
    // Simulate API call
    setTimeout(() => {
      setAnalysisData({
        symbol: symbol,
        fundamentals: {
          peRatio: '22.5',
          pbRatio: '3.2',
          roe: '14.8%',
          debtToEquity: '0.65',
          marketCap: '₹18.5L Cr',
          dividend: '1.2%'
        },
        technicals: {
          trend: 'Bullish',
          support: '24,200',
          resistance: '24,800',
          rsi: '58.5',
          macd: 'Positive',
          volume: 'Above Average'
        },
        insights: [
          {
            type: 'positive',
            title: 'Strong Fundamentals',
            description: 'P/E ratio is within historical range, indicating fair valuation'
          },
          {
            type: 'neutral',
            title: 'Technical Outlook',
            description: 'Price is consolidating near resistance levels, awaiting breakout'
          },
          {
            type: 'positive',
            title: 'Market Conditions',
            description: 'Favorable macroeconomic environment supporting growth'
          }
        ],
        recommendation: {
          score: 75,
          outlook: 'Positive',
          timeframe: '6-12 months',
          keyFactors: ['GDP growth momentum', 'Corporate earnings recovery', 'FII inflows']
        }
      });
      setIsAnalyzing(false);
    }, 2000);
  };

  return (
    <div className="min-h-screen px-4 py-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="glass-card p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Brain className="text-purple-400" size={32} />
            <h1 className="text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-500 bg-clip-text text-transparent">
              Mindsage
            </h1>
          </div>
          <p className="text-gray-300 mb-6">
            AI-powered investment guide analyzing fundamentals, technicals, and market factors for comprehensive insights
          </p>
          
          {/* Symbol Input */}
          <div className="flex space-x-4">
            <div className="flex-1">
              <Input
                placeholder="Enter symbol (e.g., NIFTY50, RELIANCE.NS)"
                value={symbol}
                onChange={(e) => setSymbol(e.target.value)}
                className="bg-white/10 border-white/20"
              />
            </div>
            <Button 
              onClick={handleAnalyze}
              disabled={isAnalyzing}
              className="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
            >
              {isAnalyzing ? (
                <>
                  <Brain className="animate-pulse mr-2" size={16} />
                  Analyzing...
                </>
              ) : (
                <>
                  <Search className="mr-2" size={16} />
                  Analyze
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Analysis Results */}
        {analysisData && (
          <div className="space-y-6">
            {/* Overall Recommendation */}
            <Card className="glass-card border-none">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="text-green-400" size={20} />
                  <span>Investment Outlook</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-400">{analysisData.recommendation.score}/100</div>
                    <div className="text-sm text-gray-400">Confidence Score</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-400">{analysisData.recommendation.outlook}</div>
                    <div className="text-sm text-gray-400">Outlook</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-400">{analysisData.recommendation.timeframe}</div>
                    <div className="text-sm text-gray-400">Timeframe</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-cyan-400">{analysisData.symbol}</div>
                    <div className="text-sm text-gray-400">Symbol</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Detailed Analysis Tabs */}
            <Tabs defaultValue="fundamentals" className="w-full">
              <TabsList className="grid w-full grid-cols-3 bg-white/10">
                <TabsTrigger value="fundamentals">Fundamentals</TabsTrigger>
                <TabsTrigger value="technicals">Technicals</TabsTrigger>
                <TabsTrigger value="insights">AI Insights</TabsTrigger>
              </TabsList>

              <TabsContent value="fundamentals" className="space-y-4">
                <Card className="glass-card border-none">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <BarChart3 className="text-blue-400" size={20} />
                      <span>Fundamental Analysis</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-3 gap-4">
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-400">P/E Ratio</span>
                          <span className="font-semibold">{analysisData.fundamentals.peRatio}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">P/B Ratio</span>
                          <span className="font-semibold">{analysisData.fundamentals.pbRatio}</span>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-400">ROE</span>
                          <span className="font-semibold text-green-400">{analysisData.fundamentals.roe}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Debt to Equity</span>
                          <span className="font-semibold">{analysisData.fundamentals.debtToEquity}</span>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Market Cap</span>
                          <span className="font-semibold">{analysisData.fundamentals.marketCap}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Dividend Yield</span>
                          <span className="font-semibold">{analysisData.fundamentals.dividend}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="technicals" className="space-y-4">
                <Card className="glass-card border-none">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <TrendingUp className="text-green-400" size={20} />
                      <span>Technical Analysis</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-3 gap-4">
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Trend</span>
                          <Badge className="bg-green-500/20 text-green-400">{analysisData.technicals.trend}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Support</span>
                          <span className="font-semibold">{analysisData.technicals.support}</span>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-400">Resistance</span>
                          <span className="font-semibold">{analysisData.technicals.resistance}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">RSI</span>
                          <span className="font-semibold">{analysisData.technicals.rsi}</span>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-400">MACD</span>
                          <Badge className="bg-blue-500/20 text-blue-400">{analysisData.technicals.macd}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-400">Volume</span>
                          <span className="font-semibold">{analysisData.technicals.volume}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="insights" className="space-y-4">
                <div className="space-y-4">
                  {analysisData.insights.map((insight, index) => (
                    <Card key={index} className="glass-card border-none">
                      <CardContent className="pt-4">
                        <div className="flex items-start space-x-3">
                          <div className={`p-2 rounded-full ${
                            insight.type === 'positive' ? 'bg-green-500/20' :
                            insight.type === 'negative' ? 'bg-red-500/20' : 'bg-yellow-500/20'
                          }`}>
                            <AlertCircle className={`${
                              insight.type === 'positive' ? 'text-green-400' :
                              insight.type === 'negative' ? 'text-red-400' : 'text-yellow-400'
                            }`} size={16} />
                          </div>
                          <div>
                            <h3 className="font-semibold mb-1">{insight.title}</h3>
                            <p className="text-gray-300 text-sm">{insight.description}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <Card className="glass-card border-none">
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <DollarSign className="text-yellow-400" size={20} />
                      <span>Key Factors</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {analysisData.recommendation.keyFactors.map((factor, index) => (
                        <Badge key={index} className="bg-purple-500/20 text-purple-400">
                          {factor}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        )}

        {/* Placeholder when no analysis */}
        {!analysisData && !isAnalyzing && (
          <Card className="glass-card border-none">
            <CardContent className="text-center py-12">
              <Brain className="mx-auto mb-4 text-purple-400" size={48} />
              <h3 className="text-xl font-semibold mb-2">Ready for Analysis</h3>
              <p className="text-gray-400">Enter a symbol above and click "Analyze" to get comprehensive investment insights</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default Mindsage;
