import React from 'react';
import { BarChart3, TrendingUp, Activity, PieChart } from 'lucide-react';

const Console = () => {
  return (
    <div className="min-h-screen px-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Console</h1>
          <p className="text-gray-400">Advanced market analysis with statistical insights and AI-driven suggestions</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-6 mb-8">
          <div className="lg:col-span-2">
            <div className="glass-card p-6">
              <h2 className="text-xl font-semibold mb-4">Symbol Analysis</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Select Symbol</label>
                  <input 
                    type="text" 
                    placeholder="e.g., NIFTY50, RELIANCE.NS" 
                    className="w-full p-3 bg-white/5 border border-white/10 rounded-lg focus:ring-2 focus:ring-cyan-400 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Timeframe</label>
                  <select className="w-full p-3 bg-white/5 border border-white/10 rounded-lg focus:ring-2 focus:ring-cyan-400 focus:border-transparent">
                    <option>Last 30 days</option>
                    <option>Intraday</option>
                    <option>Last 7 days</option>
                    <option>Last 90 days</option>
                  </select>
                </div>
                <button className="w-full glass-button py-3">
                  Analyze Symbol
                </button>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold mb-4">Quick Stats</h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-400">Current Price</span>
                  <span className="text-cyan-400">24,580</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Change</span>
                  <span className="text-green-400">+125.30 (+0.51%)</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Volume</span>
                  <span className="text-white">1.2M</span>
                </div>
              </div>
            </div>

            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold mb-4">PiP Mode</h3>
              <p className="text-sm text-gray-400 mb-4">Enable picture-in-picture for top analysis insights</p>
              <button className="w-full glass-button py-2">
                Enable PiP
              </button>
            </div>
          </div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="glass-card p-6 text-center">
            <BarChart3 className="w-8 h-8 text-cyan-400 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Statistical Analysis</h3>
            <p className="text-sm text-gray-400">Mean, std dev, variance</p>
          </div>
          <div className="glass-card p-6 text-center">
            <TrendingUp className="w-8 h-8 text-green-400 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Visual Analysis</h3>
            <p className="text-sm text-gray-400">Trend detection</p>
          </div>
          <div className="glass-card p-6 text-center">
            <Activity className="w-8 h-8 text-purple-400 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Indicator Analysis</h3>
            <p className="text-sm text-gray-400">RSI, MACD, MA</p>
          </div>
          <div className="glass-card p-6 text-center">
            <PieChart className="w-8 h-8 text-blue-400 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">AI Suggestions</h3>
            <p className="text-sm text-gray-400">Data-driven insights</p>
          </div>
        </div>

        <div className="glass-card p-6">
          <h2 className="text-xl font-semibold mb-4">Analysis Results</h2>
          <div className="text-center py-12">
            <BarChart3 size={48} className="text-gray-400 mx-auto mb-4" />
            <p className="text-gray-400">Select a symbol and timeframe to begin analysis</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Console;
