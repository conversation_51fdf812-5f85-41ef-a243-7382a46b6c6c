
import React from 'react';
import { Globe, TrendingUp, TrendingDown, Minus, ExternalLink } from 'lucide-react';

const GlobalSentiment = () => {
  const sentimentData = {
    overall: 'Positive',
    score: 72,
    trend: 'up'
  };

  const newsItems = [
    { 
      source: 'Economic Times', 
      headline: 'Nifty 50 hits new record high on strong GDP growth data',
      sentiment: 'Positive',
      impact: 'High',
      time: '2 hours ago'
    },
    { 
      source: 'Moneycontrol', 
      headline: 'RBI maintains repo rate, signals dovish stance',
      sentiment: 'Positive',
      impact: 'Medium',
      time: '4 hours ago'
    },
    { 
      source: 'Bloomberg', 
      headline: 'Global inflation concerns weigh on emerging markets',
      sentiment: 'Negative',
      impact: 'Medium',
      time: '6 hours ago'
    },
    { 
      source: 'Reuters', 
      headline: 'Tech stocks rally on AI infrastructure investments',
      sentiment: 'Positive',
      impact: 'High',
      time: '8 hours ago'
    }
  ];

  const sectorSentiment = [
    { sector: 'Technology', sentiment: 'Positive', score: 78, change: '+5' },
    { sector: 'Banking', sentiment: 'Positive', score: 68, change: '+2' },
    { sector: 'Energy', sentiment: 'Neutral', score: 52, change: '-1' },
    { sector: 'Pharma', sentiment: 'Positive', score: 65, change: '+3' },
    { sector: 'Auto', sentiment: 'Negative', score: 42, change: '-4' }
  ];

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment.toLowerCase()) {
      case 'positive': return <TrendingUp className="text-green-400" size={16} />;
      case 'negative': return <TrendingDown className="text-red-400" size={16} />;
      default: return <Minus className="text-gray-400" size={16} />;
    }
  };

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment.toLowerCase()) {
      case 'positive': return 'text-green-400';
      case 'negative': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <div className="min-h-screen px-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Global Sentiment</h1>
          <p className="text-gray-400">Real-time market sentiment analysis from global financial news and social media</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-6 mb-8">
          <div className="glass-card p-6">
            <h2 className="text-xl font-semibold mb-4">Overall Market Sentiment</h2>
            <div className="text-center">
              <div className={`text-6xl font-bold mb-2 ${getSentimentColor(sentimentData.overall)}`}>
                {sentimentData.score}
              </div>
              <div className={`text-lg font-semibold mb-2 ${getSentimentColor(sentimentData.overall)}`}>
                {sentimentData.overall}
              </div>
              <div className="flex items-center justify-center space-x-2">
                {getSentimentIcon(sentimentData.overall)}
                <span className="text-sm text-gray-400">
                  {sentimentData.trend === 'up' ? 'Improving' : 'Declining'} sentiment
                </span>
              </div>
            </div>
          </div>

          <div className="glass-card p-6">
            <h2 className="text-xl font-semibold mb-4">News Sources</h2>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Economic Times</span>
                <span className="text-green-400">Active</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Moneycontrol</span>
                <span className="text-green-400">Active</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Bloomberg</span>
                <span className="text-green-400">Active</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">Reuters</span>
                <span className="text-green-400">Active</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-400">CNBC</span>
                <span className="text-yellow-400">Pending</span>
              </div>
            </div>
          </div>

          <div className="glass-card p-6">
            <h2 className="text-xl font-semibold mb-4">Analysis Summary</h2>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-400">Articles Analyzed</span>
                <span className="text-cyan-400">847</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Positive News</span>
                <span className="text-green-400">68%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Neutral News</span>
                <span className="text-gray-400">22%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Negative News</span>
                <span className="text-red-400">10%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Last Update</span>
                <span className="text-white">5 min ago</span>
              </div>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-2 gap-6 mb-8">
          <div className="glass-card p-6">
            <h2 className="text-xl font-semibold mb-4">Sector Sentiment</h2>
            <div className="space-y-4">
              {sectorSentiment.map((sector, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getSentimentIcon(sector.sentiment)}
                    <span className="font-medium">{sector.sector}</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span className={getSentimentColor(sector.sentiment)}>{sector.score}</span>
                    <span className={`text-sm ${
                      sector.change.startsWith('+') ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {sector.change}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="glass-card p-6">
            <h2 className="text-xl font-semibold mb-4">Key Market Drivers</h2>
            <div className="space-y-4">
              <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <TrendingUp className="text-green-400" size={16} />
                  <span className="font-medium text-green-400">Positive Driver</span>
                </div>
                <p className="text-sm text-gray-300">Strong GDP growth data exceeded expectations</p>
              </div>
              <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <TrendingUp className="text-green-400" size={16} />
                  <span className="font-medium text-green-400">Positive Driver</span>
                </div>
                <p className="text-sm text-gray-300">RBI's dovish monetary policy stance</p>
              </div>
              <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <TrendingDown className="text-red-400" size={16} />
                  <span className="font-medium text-red-400">Risk Factor</span>
                </div>
                <p className="text-sm text-gray-300">Global inflation concerns persist</p>
              </div>
            </div>
          </div>
        </div>

        <div className="glass-card p-6">
          <h2 className="text-xl font-semibold mb-4">Latest News Impact</h2>
          <div className="space-y-4">
            {newsItems.map((item, index) => (
              <div key={index} className="p-4 bg-white/5 rounded-lg border border-white/10 hover:bg-white/10 transition-colors">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    {getSentimentIcon(item.sentiment)}
                    <span className="text-sm text-gray-400">{item.source}</span>
                    <span className={`text-xs px-2 py-1 rounded ${
                      item.impact === 'High' ? 'bg-red-500/20 text-red-400' :
                      item.impact === 'Medium' ? 'bg-yellow-500/20 text-yellow-400' :
                      'bg-green-500/20 text-green-400'
                    }`}>
                      {item.impact} Impact
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-500">{item.time}</span>
                    <ExternalLink size={14} className="text-gray-400 cursor-pointer hover:text-cyan-400" />
                  </div>
                </div>
                <h3 className="font-medium text-white mb-1">{item.headline}</h3>
                <div className="flex items-center justify-between">
                  <span className={`text-sm ${getSentimentColor(item.sentiment)}`}>
                    Sentiment: {item.sentiment}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GlobalSentiment;
