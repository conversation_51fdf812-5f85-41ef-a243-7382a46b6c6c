import React from 'react';
import { User, Key, FileText, Palette, Shield, Settings } from 'lucide-react';

const Profile = () => {
  return (
    <div className="min-h-screen px-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Profile</h1>
          <p className="text-gray-400">Manage your account settings, API configurations, and platform preferences</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <div className="glass-card p-6">
              <div className="flex items-center space-x-3 mb-6">
                <User className="w-6 h-6 text-cyan-400" />
                <h2 className="text-xl font-semibold">Account Details</h2>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Display Name</label>
                  <input 
                    type="text" 
                    value="John Doe" 
                    className="w-full p-3 bg-white/5 border border-white/10 rounded-lg focus:ring-2 focus:ring-cyan-400"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Email</label>
                  <input 
                    type="email" 
                    value="<EMAIL>" 
                    className="w-full p-3 bg-white/5 border border-white/10 rounded-lg focus:ring-2 focus:ring-cyan-400"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Linked Accounts</label>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                      <span className="flex items-center space-x-2">
                        <div className="w-4 h-4 bg-red-500 rounded"></div>
                        <span>Google</span>
                      </span>
                      <span className="text-green-400 text-sm">Connected</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                      <span className="flex items-center space-x-2">
                        <div className="w-4 h-4 bg-gray-800 rounded"></div>
                        <span>GitHub</span>
                      </span>
                      <button className="text-cyan-400 text-sm hover:underline">Connect</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="glass-card p-6">
              <div className="flex items-center space-x-3 mb-6">
                <Key className="w-6 h-6 text-cyan-400" />
                <h2 className="text-xl font-semibold">API Configuration</h2>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">MCP API Key</label>
                  <div className="flex space-x-2">
                    <input 
                      type="password" 
                      placeholder="••••••••••••••••" 
                      className="flex-1 p-3 bg-white/5 border border-white/10 rounded-lg"
                    />
                    <button className="glass-button px-4">Generate</button>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Broker API Settings</label>
                  <div className="space-y-2">
                    <select className="w-full p-3 bg-white/5 border border-white/10 rounded-lg">
                      <option>Select Broker</option>
                      <option>Fyers</option>
                      <option>Upstox</option>
                      <option>Zerodha</option>
                    </select>
                    <input 
                      type="password" 
                      placeholder="Broker API Key" 
                      className="w-full p-3 bg-white/5 border border-white/10 rounded-lg"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="glass-card p-6">
              <div className="flex items-center space-x-3 mb-6">
                <FileText className="w-6 h-6 text-cyan-400" />
                <h2 className="text-xl font-semibold">Statements & Logs</h2>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                  <div>
                    <p className="font-medium">Trading Activity Log</p>
                    <p className="text-sm text-gray-400">Last updated: 2 hours ago</p>
                  </div>
                  <button className="text-cyan-400 hover:underline">Download</button>
                </div>
                <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                  <div>
                    <p className="font-medium">API Call History</p>
                    <p className="text-sm text-gray-400">Last updated: 5 minutes ago</p>
                  </div>
                  <button className="text-cyan-400 hover:underline">View</button>
                </div>
                <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                  <div>
                    <p className="font-medium">System Logs</p>
                    <p className="text-sm text-gray-400">Last updated: 1 minute ago</p>
                  </div>
                  <button className="text-cyan-400 hover:underline">View</button>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="glass-card p-6">
              <div className="flex items-center space-x-3 mb-6">
                <Palette className="w-6 h-6 text-cyan-400" />
                <h2 className="text-lg font-semibold">Theme Settings</h2>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Visual Theme</label>
                  <div className="space-y-2">
                    <label className="flex items-center space-x-2">
                      <input type="radio" name="theme" defaultChecked className="text-cyan-400" />
                      <span>Dark (Liquid Glass)</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="radio" name="theme" className="text-cyan-400" />
                      <span>Light</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input type="radio" name="theme" className="text-cyan-400" />
                      <span>System</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <div className="glass-card p-6">
              <div className="flex items-center space-x-3 mb-6">
                <Shield className="w-6 h-6 text-cyan-400" />
                <h2 className="text-lg font-semibold">Admin Panel</h2>
              </div>
              
              <div className="space-y-4">
                <button className="w-full glass-button py-3 flex items-center justify-center space-x-2">
                  <Settings size={16} />
                  <span>Admin Dashboard</span>
                </button>
                <button className="w-full glass-button py-3">
                  User Management
                </button>
                <button className="w-full glass-button py-3">
                  System Monitoring
                </button>
              </div>
            </div>

            <div className="glass-card p-6">
              <button className="w-full bg-red-500/20 text-red-400 border border-red-500/30 rounded-lg py-3 hover:bg-red-500/30 transition-colors">
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
