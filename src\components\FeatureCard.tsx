
import React from 'react';
import { LucideIcon } from 'lucide-react';

interface FeatureCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  status?: 'active' | 'coming-soon';
  onClick?: () => void;
  metrics?: { label: string; value: string; trend?: 'up' | 'down' }[];
}

const FeatureCard: React.FC<FeatureCardProps> = ({
  title,
  description,
  icon: Icon,
  status = 'active',
  onClick,
  metrics = []
}) => {
  return (
    <div 
      className={`glass-card p-6 liquid-float cursor-pointer transition-all duration-300 hover:scale-105 group ${
        status === 'coming-soon' ? 'opacity-60' : ''
      }`}
      onClick={onClick}
    >
      <div className="flex items-start justify-between mb-4">
        <div className={`p-3 rounded-lg ${
          status === 'coming-soon' 
            ? 'bg-gray-500/20' 
            : 'bg-gradient-to-br from-cyan-400/20 to-blue-500/20'
        }`}>
          <Icon className={`w-6 h-6 ${
            status === 'coming-soon' ? 'text-gray-400' : 'text-cyan-400'
          }`} />
        </div>
        {status === 'coming-soon' && (
          <span className="text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded-full">
            Coming Soon
          </span>
        )}
      </div>
      
      <h3 className="text-lg font-semibold mb-2 group-hover:text-cyan-400 transition-colors">
        {title}
      </h3>
      <p className="text-gray-400 text-sm mb-4">{description}</p>
      
      {metrics.length > 0 && (
        <div className="space-y-2">
          {metrics.map((metric, index) => (
            <div key={index} className="flex justify-between items-center">
              <span className="text-xs text-gray-500">{metric.label}</span>
              <span className={`text-sm font-medium ${
                metric.trend === 'up' ? 'text-green-400' : 
                metric.trend === 'down' ? 'text-red-400' : 'text-gray-300'
              }`}>
                {metric.value}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FeatureCard;
