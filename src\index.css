
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Slynqix Liquid Glass Design System */

@layer base {
  :root {
    /* Core Liquid Glass Colors */
    --background: 225 100% 4%;
    --foreground: 210 40% 98%;
    
    /* Glass morphism variables */
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: rgba(0, 0, 0, 0.25);
    
    /* Primary brand colors - liquid blues and teals */
    --primary: 200 100% 60%;
    --primary-foreground: 225 100% 4%;
    --primary-glass: rgba(0, 191, 255, 0.1);
    
    /* Secondary - deep purple accents */
    --secondary: 260 40% 20%;
    --secondary-foreground: 210 40% 98%;
    --secondary-glass: rgba(139, 69, 255, 0.1);
    
    /* Success/Profit colors */
    --success: 145 80% 50%;
    --success-glass: rgba(34, 197, 94, 0.1);
    
    /* Danger/Loss colors */
    --destructive: 0 70% 60%;
    --destructive-glass: rgba(239, 68, 68, 0.1);
    
    /* Neutral glass elements */
    --card: rgba(255, 255, 255, 0.03);
    --card-foreground: 210 40% 98%;
    --card-border: rgba(255, 255, 255, 0.08);
    
    --muted: rgba(255, 255, 255, 0.05);
    --muted-foreground: 215.4 16.3% 65%;
    
    --accent: rgba(255, 255, 255, 0.08);
    --accent-foreground: 210 40% 98%;
    
    --border: rgba(255, 255, 255, 0.1);
    --input: rgba(255, 255, 255, 0.05);
    --ring: 200 100% 60%;
    
    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900;
    @apply text-foreground;
    @apply min-h-screen;
    background-attachment: fixed;
  }
  
  /* Liquid Glass utility classes */
  .glass {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px var(--glass-shadow);
  }
  
  .glass-card {
    @apply glass rounded-xl;
  }
  
  .glass-button {
    @apply glass rounded-lg px-4 py-2 transition-all duration-300;
    @apply hover:bg-white/10 hover:scale-105;
  }
  
  .liquid-gradient {
    background: linear-gradient(135deg, 
      rgba(0, 191, 255, 0.1) 0%, 
      rgba(139, 69, 255, 0.1) 50%, 
      rgba(0, 255, 255, 0.1) 100%);
  }
  
  .profit-glass {
    background: var(--success-glass);
    border-color: rgba(34, 197, 94, 0.2);
  }
  
  .loss-glass {
    background: var(--destructive-glass);
    border-color: rgba(239, 68, 68, 0.2);
  }
}

/* Custom scrollbar for liquid glass theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Animations */
@keyframes liquid-float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(1deg); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px rgba(0, 191, 255, 0.3); }
  50% { box-shadow: 0 0 30px rgba(0, 191, 255, 0.5); }
}

.liquid-float {
  animation: liquid-float 6s ease-in-out infinite;
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}
