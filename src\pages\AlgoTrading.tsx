
import React, { useState } from 'react';
import { TrendingUp, Play, Pause, Settings, MessageSquare, Share2 } from 'lucide-react';

const AlgoTrading = () => {
  const [selectedAlgo, setSelectedAlgo] = useState('ma-crossover');
  const [command, setCommand] = useState('');

  const predefinedAlgos = [
    { id: 'ma-crossover', name: 'MA Crossover', description: 'Buy when fast MA crosses above slow MA', status: 'active' },
    { id: 'rsi-oversold', name: 'RSI Oversold', description: 'Buy when RSI < 30, Sell when RSI > 70', status: 'paused' },
    { id: 'breakout', name: 'Breakout Strategy', description: 'Trade on price breakouts above resistance', status: 'inactive' }
  ];

  const paperTrades = [
    { id: 1, symbol: 'NIFTY50', action: 'BUY', quantity: 100, price: 24580, time: '10:30 AM', algo: 'MA Crossover', pnl: '+2.1%' },
    { id: 2, symbol: 'RELIANCE.NS', action: 'SELL', quantity: 50, price: 2850, time: '11:45 AM', algo: 'RSI Oversold', pnl: '+1.8%' },
    { id: 3, symbol: 'TCS.NS', action: 'BUY', quantity: 25, price: 4200, time: '2:15 PM', algo: 'Breakout', pnl: '-0.5%' }
  ];

  const handleCommandSubmit = () => {
    console.log('Executing command:', command);
    setCommand('');
  };

  return (
    <div className="min-h-screen px-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Algo Trading</h1>
          <p className="text-gray-400">Automated paper trading with custom algorithms and natural language commands</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-6 mb-8">
          <div className="lg:col-span-2">
            <div className="glass-card p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4">Command Trading (Paper Only)</h2>
              <div className="flex space-x-4">
                <input 
                  type="text" 
                  value={command}
                  onChange={(e) => setCommand(e.target.value)}
                  placeholder="e.g., Paper Buy 100 NIFTY50 at 24500" 
                  className="flex-1 p-3 bg-white/5 border border-white/10 rounded-lg focus:ring-2 focus:ring-cyan-400 focus:border-transparent"
                />
                <button 
                  onClick={handleCommandSubmit}
                  className="glass-button px-6"
                >
                  <MessageSquare size={18} className="mr-2" />
                  Execute
                </button>
              </div>
              <p className="text-sm text-gray-500 mt-2">Natural language paper trading commands</p>
            </div>

            <div className="glass-card p-6">
              <h2 className="text-xl font-semibold mb-4">Predefined Algorithms</h2>
              <div className="space-y-4">
                {predefinedAlgos.map((algo) => (
                  <div key={algo.id} className="p-4 bg-white/5 rounded-lg border border-white/10">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold">{algo.name}</h3>
                      <div className="flex items-center space-x-2">
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          algo.status === 'active' ? 'bg-green-500/20 text-green-400' :
                          algo.status === 'paused' ? 'bg-yellow-500/20 text-yellow-400' :
                          'bg-gray-500/20 text-gray-400'
                        }`}>
                          {algo.status}
                        </span>
                        <button className="glass-button p-1">
                          {algo.status === 'active' ? <Pause size={16} /> : <Play size={16} />}
                        </button>
                        <button className="glass-button p-1">
                          <Settings size={16} />
                        </button>
                      </div>
                    </div>
                    <p className="text-sm text-gray-400">{algo.description}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold mb-4">Paper Portfolio</h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-400">Total Value</span>
                  <span className="text-cyan-400">₹2,45,680</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Today's PnL</span>
                  <span className="text-green-400">+₹12,750 (+5.2%)</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Active Algos</span>
                  <span className="text-white">2</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Trades Today</span>
                  <span className="text-white">18</span>
                </div>
              </div>
            </div>

            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold mb-4">Algo Builder</h3>
              <p className="text-sm text-gray-400 mb-4">Create custom algorithms with visual conditions</p>
              <button className="w-full glass-button py-2">
                <Settings className="mr-2" size={16} />
                Open Builder
              </button>
            </div>

            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold mb-4">Community</h3>
              <p className="text-sm text-gray-400 mb-4">Share and discover paper trading strategies</p>
              <button className="w-full glass-button py-2">
                <Share2 className="mr-2" size={16} />
                Browse Strategies
              </button>
            </div>
          </div>
        </div>

        <div className="glass-card p-6">
          <h2 className="text-xl font-semibold mb-4">Recent Paper Trades</h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/10">
                  <th className="text-left py-3 px-4">Symbol</th>
                  <th className="text-left py-3 px-4">Action</th>
                  <th className="text-left py-3 px-4">Quantity</th>
                  <th className="text-left py-3 px-4">Price</th>
                  <th className="text-left py-3 px-4">Time</th>
                  <th className="text-left py-3 px-4">Algorithm</th>
                  <th className="text-left py-3 px-4">PnL</th>
                </tr>
              </thead>
              <tbody>
                {paperTrades.map((trade) => (
                  <tr key={trade.id} className="border-b border-white/5 hover:bg-white/5">
                    <td className="py-3 px-4 font-medium">{trade.symbol}</td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 text-xs rounded ${
                        trade.action === 'BUY' ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                      }`}>
                        {trade.action}
                      </span>
                    </td>
                    <td className="py-3 px-4">{trade.quantity}</td>
                    <td className="py-3 px-4">₹{trade.price}</td>
                    <td className="py-3 px-4 text-gray-400">{trade.time}</td>
                    <td className="py-3 px-4 text-gray-400">{trade.algo}</td>
                    <td className="py-3 px-4">
                      <span className={trade.pnl.startsWith('+') ? 'text-green-400' : 'text-red-400'}>
                        {trade.pnl}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AlgoTrading;
