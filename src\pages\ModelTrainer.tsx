
import React, { useState } from 'react';
import { Settings, Upload, FileText, Link, Youtube, Camera, Mic, Brain, Play, Trash2 } from 'lucide-react';

const ModelTrainer = () => {
  const [activeTab, setActiveTab] = useState('upload');
  const [textInput, setTextInput] = useState('');

  const trainedModels = [
    { id: 1, name: 'Technical Analysis Model', accuracy: '87%', status: 'Active', lastTrained: '2 days ago' },
    { id: 2, name: 'Fundamental Analysis Model', accuracy: '92%', status: 'Training', lastTrained: '1 hour ago' },
    { id: 3, name: 'Market Sentiment Model', accuracy: '78%', status: 'Inactive', lastTrained: '1 week ago' }
  ];

  const trainingMaterials = [
    { id: 1, type: 'text', name: 'Technical Analysis Basics', size: '2.3 KB', status: 'Processed' },
    { id: 2, type: 'link', name: 'Investopedia: Options Trading', size: 'Web Link', status: 'Processing' },
    { id: 3, type: 'text', name: '<PERSON>uffett Investment Strategy', size: '5.1 KB', status: 'Processed' }
  ];

  const handleTextSubmit = () => {
    if (textInput.trim()) {
      console.log('Submitting text for training:', textInput);
      setTextInput('');
    }
  };

  return (
    <div className="min-h-screen px-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Model Trainer</h1>
          <p className="text-gray-400">Train personalized AI models with your study materials for customized market analysis</p>
        </div>

        <div className="grid lg:grid-cols-3 gap-6 mb-8">
          <div className="lg:col-span-2">
            <div className="glass-card p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4">Material Upload & Ingestion</h2>
              
              <div className="flex space-x-4 mb-6">
                <button 
                  onClick={() => setActiveTab('upload')}
                  className={`px-4 py-2 rounded-lg transition-colors ${
                    activeTab === 'upload' ? 'bg-cyan-400/20 text-cyan-400' : 'text-gray-400 hover:text-white'
                  }`}
                >
                  <FileText size={16} className="inline mr-2" />
                  Text Upload
                </button>
                <button 
                  onClick={() => setActiveTab('link')}
                  className={`px-4 py-2 rounded-lg transition-colors ${
                    activeTab === 'link' ? 'bg-cyan-400/20 text-cyan-400' : 'text-gray-400 hover:text-white'
                  }`}
                >
                  <Link size={16} className="inline mr-2" />
                  Web Link
                </button>
                <button 
                  onClick={() => setActiveTab('future')}
                  className={`px-4 py-2 rounded-lg transition-colors opacity-60 cursor-not-allowed`}
                >
                  <Camera size={16} className="inline mr-2" />
                  OCR (Coming Soon)
                </button>
              </div>

              {activeTab === 'upload' && (
                <div className="space-y-4">
                  <textarea
                    value={textInput}
                    onChange={(e) => setTextInput(e.target.value)}
                    placeholder="Copy and paste your study materials, trading strategies, or market analysis notes here..."
                    className="w-full h-64 p-4 bg-white/5 border border-white/10 rounded-lg focus:ring-2 focus:ring-cyan-400 focus:border-transparent resize-none"
                  />
                  <button 
                    onClick={handleTextSubmit}
                    className="glass-button px-6 py-2"
                    disabled={!textInput.trim()}
                  >
                    <Upload size={16} className="mr-2" />
                    Upload Text for Training
                  </button>
                </div>
              )}

              {activeTab === 'link' && (
                <div className="space-y-4">
                  <input 
                    type="url" 
                    placeholder="Enter web URL (e.g., educational articles, research papers)" 
                    className="w-full p-3 bg-white/5 border border-white/10 rounded-lg focus:ring-2 focus:ring-cyan-400 focus:border-transparent"
                  />
                  <button className="glass-button px-6 py-2">
                    <Link size={16} className="mr-2" />
                    Extract & Process Link
                  </button>
                  <p className="text-sm text-gray-500">Supported: Articles, PDFs, research papers, and educational content</p>
                </div>
              )}

              {activeTab === 'future' && (
                <div className="space-y-4 opacity-60">
                  <div className="grid grid-cols-2 gap-4">
                    <button className="glass-card p-4 text-center cursor-not-allowed">
                      <Camera size={24} className="mx-auto mb-2 text-gray-400" />
                      <span className="text-sm">OCR Upload</span>
                    </button>
                    <button className="glass-card p-4 text-center cursor-not-allowed">
                      <Youtube size={24} className="mx-auto mb-2 text-gray-400" />
                      <span className="text-sm">YouTube Transcript</span>
                    </button>
                  </div>
                  <p className="text-sm text-gray-500">Additional upload methods coming soon</p>
                </div>
              )}
            </div>

            <div className="glass-card p-6">
              <h2 className="text-xl font-semibold mb-4">Training Materials</h2>
              <div className="space-y-3">
                {trainingMaterials.map((material) => (
                  <div key={material.id} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                    <div className="flex items-center space-x-3">
                      {material.type === 'text' ? <FileText size={16} className="text-cyan-400" /> : <Link size={16} className="text-blue-400" />}
                      <div>
                        <span className="font-medium">{material.name}</span>
                        <div className="text-sm text-gray-400">{material.size}</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        material.status === 'Processed' ? 'bg-green-500/20 text-green-400' :
                        material.status === 'Processing' ? 'bg-yellow-500/20 text-yellow-400' :
                        'bg-gray-500/20 text-gray-400'
                      }`}>
                        {material.status}
                      </span>
                      <button className="glass-button p-1">
                        <Trash2 size={14} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold mb-4">Training Status</h3>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-400">Materials Uploaded</span>
                  <span className="text-cyan-400">12</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Processing Queue</span>
                  <span className="text-yellow-400">2</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Active Models</span>
                  <span className="text-green-400">3</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Last Training</span>
                  <span className="text-white">1 hour ago</span>
                </div>
              </div>
            </div>

            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button className="w-full glass-button py-2">
                  <Brain className="mr-2" size={16} />
                  Start Training
                </button>
                <button className="w-full glass-button py-2">
                  <Settings className="mr-2" size={16} />
                  Training Config
                </button>
                <button className="w-full glass-button py-2">
                  <Play className="mr-2" size={16} />
                  Test Model
                </button>
              </div>
            </div>

            <div className="glass-card p-6">
              <h3 className="text-lg font-semibold mb-4">Compute Resources</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-400">GPU Usage</span>
                  <span className="text-cyan-400">23%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Memory</span>
                  <span className="text-yellow-400">67%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Queue Position</span>
                  <span className="text-white">-</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="glass-card p-6">
          <h2 className="text-xl font-semibold mb-4">Your Trained Models</h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-white/10">
                  <th className="text-left py-3 px-4">Model Name</th>
                  <th className="text-left py-3 px-4">Accuracy</th>
                  <th className="text-left py-3 px-4">Status</th>
                  <th className="text-left py-3 px-4">Last Trained</th>
                  <th className="text-left py-3 px-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {trainedModels.map((model) => (
                  <tr key={model.id} className="border-b border-white/5 hover:bg-white/5">
                    <td className="py-3 px-4 font-medium">{model.name}</td>
                    <td className="py-3 px-4 text-green-400">{model.accuracy}</td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        model.status === 'Active' ? 'bg-green-500/20 text-green-400' :
                        model.status === 'Training' ? 'bg-yellow-500/20 text-yellow-400' :
                        'bg-gray-500/20 text-gray-400'
                      }`}>
                        {model.status}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-gray-400">{model.lastTrained}</td>
                    <td className="py-3 px-4">
                      <div className="flex space-x-2">
                        <button className="glass-button p-1">
                          <Play size={14} />
                        </button>
                        <button className="glass-button p-1">
                          <Settings size={14} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModelTrainer;
